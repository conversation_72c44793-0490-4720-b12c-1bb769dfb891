"""
统一请求监控中间件
集成性能监控和日志记录功能
"""
import time
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.utils.logger import operation_logger, setup_logger

logger = setup_logger()


class UnifiedRequestMiddleware(BaseHTTPMiddleware):
    """
    统一请求监控中间件
    集成性能监控和日志记录功能，避免重复处理
    """

    def __init__(self, app, log_slow_requests: bool = True, slow_threshold: float = 1.0):
        super().__init__(app)
        self.log_slow_requests = log_slow_requests
        self.slow_threshold = slow_threshold

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求并记录日志和性能指标

        Args:
            request: HTTP请求
            call_next: 下一个处理器

        Returns:
            HTTP响应
        """
        start_time = time.time()

        # 获取请求信息
        method = request.method
        url = str(request.url)
        path = request.url.path

        # 跳过OPTIONS请求和静态资源的日志记录
        skip_logging = (
            method == "OPTIONS" or
            path.startswith("/docs") or
            path.startswith("/redoc") or
            path.startswith("/openapi.json") or
            path.startswith("/uploads/") or
            path.startswith("/static/")
        )

        headers = dict(request.headers)
        client_ip = request.client.host if request.client else "unknown"

        # 记录请求开始
        request_data = {
            "method": method,
            "url": url,
            "client_ip": client_ip,
            "user_agent": headers.get("user-agent", "unknown")
        }

        try:
            # 处理请求
            response = await call_next(request)

            # 计算处理时间
            process_time = time.time() - start_time

            # 添加性能监控响应头
            response.headers["X-Process-Time"] = f"{process_time:.3f}"

            # 性能警告日志
            if self.log_slow_requests:
                if process_time > self.slow_threshold:
                    logger.warning(
                        f"慢请求警告 - {method} {request.url.path} - "
                        f"处理时间: {process_time:.3f}s"
                    )
                elif process_time > 0.5:
                    logger.info(
                        f"性能提醒 - {method} {request.url.path} - "
                        f"处理时间: {process_time:.3f}s"
                    )

            # 记录成功请求（跳过OPTIONS和静态资源请求）
            if not skip_logging and (process_time > 0.1 or response.status_code >= 400):
                await operation_logger.log_operation(
                    operation="http_request",
                    request_data={
                        **request_data,
                        "status_code": response.status_code,
                        "process_time": f"{process_time:.3f}s"
                    },
                    result="success" if response.status_code < 400 else "warning"
                )

            return response

        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time

            # 记录失败请求（跳过OPTIONS和静态资源请求）
            if not skip_logging:
                await operation_logger.log_operation(
                    operation="http_request",
                    request_data={
                        **request_data,
                        "process_time": f"{process_time:.3f}s"
                    },
                    result="failed",
                    error_msg=str(e)
                )

            # 重新抛出异常
            raise